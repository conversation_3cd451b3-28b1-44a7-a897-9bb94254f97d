#!/usr/bin/env python3
"""
DSB Lead Data Analyzer
Analyzes lead data and status from Excel files in the current directory.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

class LeadDataAnalyzer:
    def __init__(self):
        self.lead_data = None
        self.status_data = None
        self.analysis_results = {}
        
    def load_data(self):
        """Load Excel files and examine their structure"""
        try:
            print("Loading DSB Lead Data...")
            self.lead_data = pd.read_excel('DSB_LeadData.xlsx')
            print(f"Lead data loaded: {self.lead_data.shape[0]} rows, {self.lead_data.shape[1]} columns")
            
            print("\nLoading DSB Service-wise Status Data...")
            self.status_data = pd.read_excel('DSBServiceWiseStatus3 1.xlsx')
            print(f"Status data loaded: {self.status_data.shape[0]} rows, {self.status_data.shape[1]} columns")
            
            return True
        except Exception as e:
            print(f"Error loading data: {e}")
            return False
    
    def examine_data_structure(self):
        """Examine the structure and content of the loaded data"""
        print("\n" + "="*60)
        print("DATA STRUCTURE ANALYSIS")
        print("="*60)
        
        if self.lead_data is not None:
            print("\n📊 LEAD DATA STRUCTURE:")
            print(f"Shape: {self.lead_data.shape}")
            print(f"Columns: {list(self.lead_data.columns)}")
            print(f"Data types:\n{self.lead_data.dtypes}")
            print(f"\nFirst few rows:")
            print(self.lead_data.head())
            
            print(f"\nMissing values:")
            print(self.lead_data.isnull().sum())
        
        if self.status_data is not None:
            print("\n📈 STATUS DATA STRUCTURE:")
            print(f"Shape: {self.status_data.shape}")
            print(f"Columns: {list(self.status_data.columns)}")
            print(f"Data types:\n{self.status_data.dtypes}")
            print(f"\nFirst few rows:")
            print(self.status_data.head())
            
            print(f"\nMissing values:")
            print(self.status_data.isnull().sum())
    
    def analyze_lead_status(self):
        """Analyze lead status distribution and trends"""
        print("\n" + "="*60)
        print("LEAD STATUS ANALYSIS")
        print("="*60)
        
        if self.lead_data is None:
            print("No lead data available for analysis")
            return
        
        # Look for status-related columns
        status_columns = [col for col in self.lead_data.columns if 'status' in col.lower()]
        if not status_columns:
            # Look for other potential status indicators
            potential_status_cols = [col for col in self.lead_data.columns 
                                   if any(keyword in col.lower() for keyword in 
                                         ['stage', 'state', 'condition', 'progress', 'outcome'])]
            status_columns.extend(potential_status_cols)
        
        print(f"Identified status columns: {status_columns}")
        
        for col in status_columns:
            if col in self.lead_data.columns:
                print(f"\n📊 Analysis for '{col}':")
                value_counts = self.lead_data[col].value_counts()
                print(value_counts)
                
                # Calculate percentages
                percentages = (value_counts / len(self.lead_data) * 100).round(2)
                print(f"\nPercentages:")
                for status, count in value_counts.items():
                    pct = percentages[status]
                    print(f"  {status}: {count} ({pct}%)")
    
    def analyze_service_wise_status(self):
        """Analyze service-wise status data"""
        print("\n" + "="*60)
        print("SERVICE-WISE STATUS ANALYSIS")
        print("="*60)
        
        if self.status_data is None:
            print("No status data available for analysis")
            return
        
        # Look for service and status columns
        service_columns = [col for col in self.status_data.columns if 'service' in col.lower()]
        status_columns = [col for col in self.status_data.columns if 'status' in col.lower()]
        
        print(f"Service columns: {service_columns}")
        print(f"Status columns: {status_columns}")
        
        # Analyze each service column
        for col in service_columns:
            if col in self.status_data.columns:
                print(f"\n📈 Service analysis for '{col}':")
                value_counts = self.status_data[col].value_counts()
                print(value_counts)
        
        # Analyze each status column
        for col in status_columns:
            if col in self.status_data.columns:
                print(f"\n📊 Status analysis for '{col}':")
                value_counts = self.status_data[col].value_counts()
                print(value_counts)
    
    def generate_summary_statistics(self):
        """Generate comprehensive summary statistics"""
        print("\n" + "="*60)
        print("SUMMARY STATISTICS")
        print("="*60)
        
        summary = {}
        
        if self.lead_data is not None:
            summary['lead_data'] = {
                'total_records': len(self.lead_data),
                'total_columns': len(self.lead_data.columns),
                'missing_data_percentage': (self.lead_data.isnull().sum().sum() / 
                                          (len(self.lead_data) * len(self.lead_data.columns)) * 100).round(2)
            }
            
            # Look for date columns
            date_columns = [col for col in self.lead_data.columns 
                          if self.lead_data[col].dtype == 'datetime64[ns]' or 
                          'date' in col.lower() or 'time' in col.lower()]
            
            if date_columns:
                summary['lead_data']['date_range'] = {}
                for col in date_columns:
                    try:
                        if self.lead_data[col].dtype != 'datetime64[ns]':
                            self.lead_data[col] = pd.to_datetime(self.lead_data[col], errors='coerce')
                        
                        min_date = self.lead_data[col].min()
                        max_date = self.lead_data[col].max()
                        summary['lead_data']['date_range'][col] = {
                            'from': min_date,
                            'to': max_date
                        }
                    except:
                        continue
        
        if self.status_data is not None:
            summary['status_data'] = {
                'total_records': len(self.status_data),
                'total_columns': len(self.status_data.columns),
                'missing_data_percentage': (self.status_data.isnull().sum().sum() / 
                                          (len(self.status_data) * len(self.status_data.columns)) * 100).round(2)
            }
        
        # Print summary
        for data_type, stats in summary.items():
            print(f"\n📋 {data_type.upper().replace('_', ' ')}:")
            for key, value in stats.items():
                if key == 'date_range':
                    print(f"  {key}:")
                    for date_col, date_range in value.items():
                        print(f"    {date_col}: {date_range['from']} to {date_range['to']}")
                else:
                    print(f"  {key}: {value}")
        
        self.analysis_results['summary'] = summary
        return summary
    
    def create_visualizations(self):
        """Create basic visualizations for the data"""
        print("\n" + "="*60)
        print("CREATING VISUALIZATIONS")
        print("="*60)
        
        plt.style.use('default')
        fig_count = 0
        
        # Visualize lead data status distribution
        if self.lead_data is not None:
            status_columns = [col for col in self.lead_data.columns if 'status' in col.lower()]
            
            for col in status_columns[:2]:  # Limit to first 2 status columns
                if col in self.lead_data.columns:
                    plt.figure(figsize=(10, 6))
                    value_counts = self.lead_data[col].value_counts()
                    
                    # Create bar plot
                    plt.subplot(1, 2, 1)
                    value_counts.plot(kind='bar', color='skyblue')
                    plt.title(f'Lead Status Distribution - {col}')
                    plt.xlabel('Status')
                    plt.ylabel('Count')
                    plt.xticks(rotation=45)
                    
                    # Create pie chart
                    plt.subplot(1, 2, 2)
                    plt.pie(value_counts.values, labels=value_counts.index, autopct='%1.1f%%')
                    plt.title(f'Lead Status Percentage - {col}')
                    
                    plt.tight_layout()
                    plt.savefig(f'lead_status_analysis_{col.replace(" ", "_")}.png', dpi=300, bbox_inches='tight')
                    plt.show()
                    fig_count += 1
        
        print(f"Created {fig_count} visualization(s)")
    
    def run_complete_analysis(self):
        """Run the complete analysis pipeline"""
        print("🚀 Starting DSB Lead Data Analysis")
        print("="*60)
        
        # Load data
        if not self.load_data():
            return
        
        # Examine structure
        self.examine_data_structure()
        
        # Analyze lead status
        self.analyze_lead_status()
        
        # Analyze service-wise status
        self.analyze_service_wise_status()
        
        # Generate summary
        self.generate_summary_statistics()
        
        # Create visualizations
        self.create_visualizations()
        
        print("\n✅ Analysis completed successfully!")
        print("📊 Check the generated PNG files for visualizations")

if __name__ == "__main__":
    analyzer = LeadDataAnalyzer()
    analyzer.run_complete_analysis()

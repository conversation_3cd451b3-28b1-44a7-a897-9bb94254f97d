import React, { useState } from 'react';
import { useData } from '../context/DataContext';
import DataGrid from '../components/Grid/DataGrid';
import { 
  FiGrid, 
  FiActivity, 
  FiBarChart3, 
  FiTrendingUp, 
  FiDownload,
  FiUpload,
  FiFilter,
  FiSearch
} from 'react-icons/fi';

const GridReports = () => {
  const { leadData, statusData, loading } = useData();
  const [selectedGrid, setSelectedGrid] = useState('leads');
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  const gridTypes = [
    { id: 'leads', name: 'Lead Data Grid', icon: FiGrid, description: 'Complete lead information' },
    { id: 'status', name: 'Service Status Grid', icon: FiActivity, description: 'Service performance data' },
    { id: 'performance', name: 'Performance Summary', icon: FiBarChart3, description: 'Key metrics summary' },
    { id: 'analytics', name: 'Detailed Analytics', icon: FiTrendingUp, description: 'Advanced analytics data' },
    { id: 'export', name: 'Export/Import Data', icon: FiDownload, description: 'Data management tools' }
  ];

  // Filter and search data
  const filteredLeadData = leadData.filter(lead => {
    const matchesSearch = searchTerm === '' || 
      lead.company.toLowerCase().includes(searchTerm.toLowerCase()) ||
      lead.contact.toLowerCase().includes(searchTerm.toLowerCase()) ||
      lead.email.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = filterStatus === 'all' || lead.status === filterStatus;
    
    return matchesSearch && matchesStatus;
  });

  // Grid 1: Lead Data Grid
  const leadGridColumns = [
    { field: 'id', headerName: 'Lead ID', width: 120, sortable: true },
    { field: 'company', headerName: 'Company', width: 180, sortable: true },
    { field: 'contact', headerName: 'Contact', width: 150, sortable: true },
    { field: 'email', headerName: 'Email', width: 200, sortable: true },
    { field: 'phone', headerName: 'Phone', width: 140 },
    { field: 'status', headerName: 'Status', width: 120, sortable: true },
    { field: 'source', headerName: 'Source', width: 120, sortable: true },
    { field: 'service', headerName: 'Service', width: 150, sortable: true },
    { field: 'region', headerName: 'Region', width: 100, sortable: true },
    { field: 'value', headerName: 'Value', width: 120, sortable: true, type: 'currency' },
    { field: 'probability', headerName: 'Probability', width: 100, sortable: true, type: 'percentage' },
    { field: 'createdDate', headerName: 'Created', width: 120, sortable: true, type: 'date' },
    { field: 'lastActivity', headerName: 'Last Activity', width: 120, sortable: true, type: 'date' },
    { field: 'assignedTo', headerName: 'Assigned To', width: 140 },
    { field: 'priority', headerName: 'Priority', width: 100, sortable: true }
  ];

  // Grid 2: Service Status Grid
  const serviceGridColumns = [
    { field: 'id', headerName: 'ID', width: 80 },
    { field: 'service', headerName: 'Service', width: 180, sortable: true },
    { field: 'totalLeads', headerName: 'Total Leads', width: 120, sortable: true },
    { field: 'activeLeads', headerName: 'Active Leads', width: 120, sortable: true },
    { field: 'convertedLeads', headerName: 'Converted', width: 120, sortable: true },
    { field: 'conversionRate', headerName: 'Conversion Rate', width: 140, sortable: true, type: 'percentage' },
    { field: 'revenue', headerName: 'Revenue', width: 140, sortable: true, type: 'currency' },
    { field: 'avgDealSize', headerName: 'Avg Deal Size', width: 140, sortable: true, type: 'currency' },
    { field: 'avgSalesCycle', headerName: 'Avg Sales Cycle', width: 140, sortable: true },
    { field: 'lastUpdated', headerName: 'Last Updated', width: 140, type: 'date' }
  ];

  // Grid 3: Performance Summary
  const performanceSummaryData = [
    {
      id: 1,
      metric: 'Total Leads',
      value: leadData.length,
      target: 600,
      achievement: ((leadData.length / 600) * 100).toFixed(1),
      trend: '+12%',
      period: 'This Month'
    },
    {
      id: 2,
      metric: 'Conversion Rate',
      value: ((leadData.filter(l => l.status === 'Closed Won').length / leadData.length) * 100).toFixed(1),
      target: 25,
      achievement: (((leadData.filter(l => l.status === 'Closed Won').length / leadData.length) * 100) / 25 * 100).toFixed(1),
      trend: '+2.3%',
      period: 'This Month'
    },
    {
      id: 3,
      metric: 'Revenue',
      value: (leadData.filter(l => l.status === 'Closed Won').reduce((sum, l) => sum + l.value, 0) / 1000000).toFixed(1),
      target: 5.0,
      achievement: ((leadData.filter(l => l.status === 'Closed Won').reduce((sum, l) => sum + l.value, 0) / 1000000) / 5.0 * 100).toFixed(1),
      trend: '+8%',
      period: 'This Month'
    }
  ];

  const performanceGridColumns = [
    { field: 'metric', headerName: 'Metric', width: 200 },
    { field: 'value', headerName: 'Current Value', width: 150 },
    { field: 'target', headerName: 'Target', width: 120 },
    { field: 'achievement', headerName: 'Achievement %', width: 150, type: 'percentage' },
    { field: 'trend', headerName: 'Trend', width: 120 },
    { field: 'period', headerName: 'Period', width: 150 }
  ];

  // Grid 4: Detailed Analytics
  const analyticsData = leadData.map(lead => {
    const daysInPipeline = Math.floor((new Date() - new Date(lead.createdDate)) / (1000 * 60 * 60 * 24));
    const expectedCloseDate = new Date(new Date(lead.createdDate).getTime() + (60 * 24 * 60 * 60 * 1000)); // 60 days
    
    return {
      ...lead,
      daysInPipeline,
      expectedCloseDate: expectedCloseDate.toISOString().split('T')[0],
      weightedValue: (lead.value * lead.probability / 100).toFixed(0),
      riskLevel: daysInPipeline > 90 ? 'High' : daysInPipeline > 60 ? 'Medium' : 'Low'
    };
  });

  const analyticsGridColumns = [
    { field: 'id', headerName: 'Lead ID', width: 120 },
    { field: 'company', headerName: 'Company', width: 180 },
    { field: 'status', headerName: 'Status', width: 120 },
    { field: 'value', headerName: 'Value', width: 120, type: 'currency' },
    { field: 'probability', headerName: 'Probability', width: 100, type: 'percentage' },
    { field: 'weightedValue', headerName: 'Weighted Value', width: 140, type: 'currency' },
    { field: 'daysInPipeline', headerName: 'Days in Pipeline', width: 140 },
    { field: 'expectedCloseDate', headerName: 'Expected Close', width: 140, type: 'date' },
    { field: 'riskLevel', headerName: 'Risk Level', width: 120 },
    { field: 'assignedTo', headerName: 'Assigned To', width: 140 }
  ];

  const getCurrentGridData = () => {
    switch (selectedGrid) {
      case 'leads':
        return { data: filteredLeadData, columns: leadGridColumns };
      case 'status':
        return { data: statusData, columns: serviceGridColumns };
      case 'performance':
        return { data: performanceSummaryData, columns: performanceGridColumns };
      case 'analytics':
        return { data: analyticsData, columns: analyticsGridColumns };
      default:
        return { data: [], columns: [] };
    }
  };

  const handleExport = (format) => {
    const { data } = getCurrentGridData();
    console.log(`Exporting ${data.length} records as ${format}`);
    // Implementation would go here
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="dashboard-header">
        <h1 className="text-3xl font-bold mb-2">Grid Reports</h1>
        <p className="text-blue-100">
          5 Interactive data grids with sorting, filtering, and export capabilities
        </p>
      </div>

      {/* Grid Type Selector */}
      <div className="bg-white rounded-lg shadow-sm p-4">
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
          {gridTypes.map((grid) => (
            <button
              key={grid.id}
              onClick={() => setSelectedGrid(grid.id)}
              className={`p-4 rounded-lg border-2 transition-all ${
                selectedGrid === grid.id
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
            >
              <div className="flex flex-col items-center text-center space-y-2">
                <grid.icon 
                  size={24} 
                  className={selectedGrid === grid.id ? 'text-blue-600' : 'text-gray-400'} 
                />
                <div>
                  <h3 className={`font-medium ${
                    selectedGrid === grid.id ? 'text-blue-800' : 'text-gray-800'
                  }`}>
                    {grid.name}
                  </h3>
                  <p className="text-xs text-gray-600">{grid.description}</p>
                </div>
              </div>
            </button>
          ))}
        </div>
      </div>

      {/* Controls */}
      {selectedGrid !== 'export' && (
        <div className="bg-white rounded-lg shadow-sm p-4">
          <div className="flex flex-wrap gap-4 items-center justify-between">
            <div className="flex items-center space-x-4">
              {/* Search */}
              <div className="relative">
                <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
                <input
                  type="text"
                  placeholder="Search..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              {/* Status Filter */}
              {selectedGrid === 'leads' && (
                <div className="flex items-center space-x-2">
                  <FiFilter className="text-gray-400" size={16} />
                  <select
                    value={filterStatus}
                    onChange={(e) => setFilterStatus(e.target.value)}
                    className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="all">All Status</option>
                    <option value="New">New</option>
                    <option value="Contacted">Contacted</option>
                    <option value="Qualified">Qualified</option>
                    <option value="Proposal">Proposal</option>
                    <option value="Negotiation">Negotiation</option>
                    <option value="Closed Won">Closed Won</option>
                    <option value="Closed Lost">Closed Lost</option>
                  </select>
                </div>
              )}
            </div>

            {/* Export Buttons */}
            <div className="flex items-center space-x-2">
              <button
                onClick={() => handleExport('csv')}
                className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
              >
                <FiDownload size={16} />
                <span>CSV</span>
              </button>
              <button
                onClick={() => handleExport('excel')}
                className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                <FiDownload size={16} />
                <span>Excel</span>
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Grid Content */}
      {selectedGrid === 'export' ? (
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">Export/Import Data Management</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Export Section */}
            <div className="space-y-4">
              <h4 className="font-medium text-gray-700">Export Data</h4>
              <div className="space-y-3">
                <button className="w-full flex items-center justify-between p-4 border border-gray-300 rounded-lg hover:bg-gray-50">
                  <div className="flex items-center space-x-3">
                    <FiDownload className="text-green-600" size={20} />
                    <div>
                      <p className="font-medium">Export All Leads</p>
                      <p className="text-sm text-gray-600">{leadData.length} records</p>
                    </div>
                  </div>
                  <span className="text-gray-400">CSV, Excel</span>
                </button>
                
                <button className="w-full flex items-center justify-between p-4 border border-gray-300 rounded-lg hover:bg-gray-50">
                  <div className="flex items-center space-x-3">
                    <FiDownload className="text-blue-600" size={20} />
                    <div>
                      <p className="font-medium">Export Service Data</p>
                      <p className="text-sm text-gray-600">{statusData.length} services</p>
                    </div>
                  </div>
                  <span className="text-gray-400">CSV, Excel</span>
                </button>
                
                <button className="w-full flex items-center justify-between p-4 border border-gray-300 rounded-lg hover:bg-gray-50">
                  <div className="flex items-center space-x-3">
                    <FiDownload className="text-purple-600" size={20} />
                    <div>
                      <p className="font-medium">Export Analytics Report</p>
                      <p className="text-sm text-gray-600">Complete analysis</p>
                    </div>
                  </div>
                  <span className="text-gray-400">PDF, Excel</span>
                </button>
              </div>
            </div>

            {/* Import Section */}
            <div className="space-y-4">
              <h4 className="font-medium text-gray-700">Import Data</h4>
              <div className="space-y-3">
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                  <FiUpload className="mx-auto text-gray-400 mb-2" size={32} />
                  <p className="text-gray-600 mb-2">Drop files here or click to upload</p>
                  <p className="text-sm text-gray-500">Supports CSV, Excel files</p>
                  <button className="mt-3 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
                    Choose Files
                  </button>
                </div>
                
                <div className="bg-yellow-50 p-4 rounded-lg">
                  <h5 className="font-medium text-yellow-800 mb-2">Import Guidelines</h5>
                  <ul className="text-sm text-yellow-700 space-y-1">
                    <li>• Ensure column headers match expected format</li>
                    <li>• Maximum file size: 10MB</li>
                    <li>• Duplicate records will be skipped</li>
                    <li>• Data validation will be performed</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div className="bg-white rounded-lg shadow-sm">
          <DataGrid
            data={getCurrentGridData().data}
            columns={getCurrentGridData().columns}
            title={gridTypes.find(g => g.id === selectedGrid)?.name}
          />
        </div>
      )}
    </div>
  );
};

export default GridReports;

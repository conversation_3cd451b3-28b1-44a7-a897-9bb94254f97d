import React from 'react';
import { NavLink } from 'react-router-dom';
import { 
  FiHome, 
  FiBarChart3, 
  FiTrendingUp, 
  FiGrid, 
  FiPieChart,
  FiActivity,
  FiTarget,
  FiUsers,
  FiDollarSign,
  FiCalendar
} from 'react-icons/fi';

const Sidebar = ({ isOpen }) => {
  const menuItems = [
    {
      title: 'Dashboard',
      icon: FiHome,
      path: '/dashboard',
      description: 'Overview & KPIs'
    },
    {
      title: 'Graphical Reports',
      icon: FiBarChart3,
      path: '/graphical-reports',
      description: '8 Chart Types',
      submenu: [
        { title: 'Lead Status Distribution', icon: FiPieChart },
        { title: 'Service Performance', icon: FiBarChart3 },
        { title: 'Monthly Trends', icon: FiTrendingUp },
        { title: 'Lead Sources', icon: FiTarget },
        { title: 'Conversion Funnel', icon: FiActivity },
        { title: 'Geographic Distribution', icon: FiUsers },
        { title: 'Time-based Activity', icon: FiCalendar },
        { title: 'Performance Metrics', icon: FiDollarSign }
      ]
    },
    {
      title: 'Analysis Reports',
      icon: FiTrendingUp,
      path: '/analysis-reports',
      description: '7 Analytics',
      submenu: [
        { title: 'Lead Conversion Analysis', icon: FiTarget },
        { title: 'Service Performance Analytics', icon: FiBarChart3 },
        { title: 'Trend Analysis', icon: FiTrendingUp },
        { title: 'Comparative Analysis', icon: FiActivity },
        { title: 'Predictive Analytics', icon: FiUsers },
        { title: 'ROI Analysis', icon: FiDollarSign },
        { title: 'Customer Segmentation', icon: FiPieChart }
      ]
    },
    {
      title: 'Grid Reports',
      icon: FiGrid,
      path: '/grid-reports',
      description: '5 Data Grids',
      submenu: [
        { title: 'Lead Data Grid', icon: FiGrid },
        { title: 'Service Status Grid', icon: FiActivity },
        { title: 'Performance Summary', icon: FiBarChart3 },
        { title: 'Detailed Analytics', icon: FiTrendingUp },
        { title: 'Export/Import Data', icon: FiDollarSign }
      ]
    }
  ];

  return (
    <aside className={`bg-gray-900 text-white transition-all duration-300 ${isOpen ? 'w-64' : 'w-16'} min-h-screen`}>
      <div className="p-4">
        <div className="flex items-center space-x-2">
          <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
            <FiBarChart3 className="text-white" size={20} />
          </div>
          {isOpen && (
            <div>
              <h2 className="text-lg font-bold">DSB Reports</h2>
              <p className="text-xs text-gray-400">Analytics Dashboard</p>
            </div>
          )}
        </div>
      </div>
      
      <nav className="mt-8">
        {menuItems.map((item, index) => (
          <div key={index} className="mb-2">
            <NavLink
              to={item.path}
              className={({ isActive }) =>
                `flex items-center px-4 py-3 text-sm font-medium transition-colors duration-200 ${
                  isActive
                    ? 'bg-blue-600 text-white border-r-4 border-blue-400'
                    : 'text-gray-300 hover:bg-gray-800 hover:text-white'
                }`
              }
            >
              <item.icon size={20} />
              {isOpen && (
                <div className="ml-3">
                  <div className="font-medium">{item.title}</div>
                  <div className="text-xs text-gray-400">{item.description}</div>
                </div>
              )}
            </NavLink>
            
            {/* Submenu items (shown when sidebar is open) */}
            {isOpen && item.submenu && (
              <div className="ml-8 mt-2 space-y-1">
                {item.submenu.map((subItem, subIndex) => (
                  <div
                    key={subIndex}
                    className="flex items-center px-4 py-2 text-xs text-gray-400 hover:text-gray-300"
                  >
                    <subItem.icon size={14} />
                    <span className="ml-2">{subItem.title}</span>
                  </div>
                ))}
              </div>
            )}
          </div>
        ))}
      </nav>
      
      {isOpen && (
        <div className="absolute bottom-4 left-4 right-4">
          <div className="bg-gray-800 rounded-lg p-3">
            <h4 className="text-sm font-medium text-white mb-1">Need Help?</h4>
            <p className="text-xs text-gray-400 mb-2">Check our documentation</p>
            <button className="w-full bg-blue-600 text-white text-xs py-2 rounded hover:bg-blue-700 transition-colors">
              View Docs
            </button>
          </div>
        </div>
      )}
    </aside>
  );
};

export default Sidebar;

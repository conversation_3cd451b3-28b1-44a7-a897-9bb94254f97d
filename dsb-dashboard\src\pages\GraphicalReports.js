import React, { useState } from 'react';
import { useData } from '../context/DataContext';
import ChartCard from '../components/Charts/ChartCard';
import { FiBarChart3, FiPieChart, FiTrendingUp, FiTarget } from 'react-icons/fi';

const GraphicalReports = () => {
  const { 
    leadData, 
    statusData, 
    loading, 
    getLeadsByStatus, 
    getLeadsBySource, 
    getLeadsByService,
    getLeadsByRegion,
    getMonthlyTrends 
  } = useData();

  const [selectedChart, setSelectedChart] = useState('all');

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  const chartTypes = [
    { id: 'all', name: 'All Charts', icon: FiBarChart3 },
    { id: 'pie', name: 'Pie Charts', icon: FiPie<PERSON>hart },
    { id: 'bar', name: 'Bar Charts', icon: FiBarChart3 },
    { id: 'line', name: 'Line Charts', icon: FiTrendingUp },
    { id: 'area', name: 'Area Charts', icon: FiTarget }
  ];

  // Prepare data for different charts
  const statusData_chart = getLeadsByStatus();
  const sourceData = getLeadsBySource();
  const serviceData = getLeadsByService();
  const regionData = getLeadsByRegion();
  const monthlyData = getMonthlyTrends();

  // Convert monthly data for charts
  const monthlyChartData = Object.entries(monthlyData)
    .sort()
    .slice(-12)
    .map(([month, data]) => ({
      month: month.substring(5),
      leads: data.leads,
      value: data.value / 1000 // Convert to thousands
    }));

  // Service performance data
  const servicePerformanceData = statusData.map(service => ({
    name: service.service,
    leads: service.totalLeads,
    converted: service.convertedLeads,
    revenue: service.revenue / 1000 // Convert to thousands
  }));

  // Conversion funnel data
  const funnelData = [
    { stage: 'Leads', count: leadData.length, percentage: 100 },
    { stage: 'Contacted', count: leadData.filter(l => l.status !== 'New').length, percentage: 85 },
    { stage: 'Qualified', count: leadData.filter(l => ['Qualified', 'Proposal', 'Negotiation', 'Closed Won'].includes(l.status)).length, percentage: 60 },
    { stage: 'Proposal', count: leadData.filter(l => ['Proposal', 'Negotiation', 'Closed Won'].includes(l.status)).length, percentage: 40 },
    { stage: 'Closed Won', count: leadData.filter(l => l.status === 'Closed Won').length, percentage: 25 }
  ];

  // Geographic distribution (mock data)
  const geoData = Object.entries(regionData).map(([region, count]) => ({
    region,
    count,
    percentage: ((count / leadData.length) * 100).toFixed(1)
  }));

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="dashboard-header">
        <h1 className="text-3xl font-bold mb-2">Graphical Reports</h1>
        <p className="text-blue-100">
          8 Different chart types for comprehensive data visualization
        </p>
      </div>

      {/* Chart Type Filter */}
      <div className="bg-white rounded-lg shadow-sm p-4">
        <div className="flex flex-wrap gap-2">
          {chartTypes.map((type) => (
            <button
              key={type.id}
              onClick={() => setSelectedChart(type.id)}
              className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
                selectedChart === type.id
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              <type.icon size={16} />
              <span>{type.name}</span>
            </button>
          ))}
        </div>
      </div>

      {/* Charts Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 1. Lead Status Distribution (Pie Chart) */}
        {(selectedChart === 'all' || selectedChart === 'pie') && (
          <ChartCard
            title="1. Lead Status Distribution"
            subtitle="Distribution of leads by current status"
            type="pie"
            data={statusData_chart}
            height={300}
          />
        )}

        {/* 2. Service-wise Performance (Bar Chart) */}
        {(selectedChart === 'all' || selectedChart === 'bar') && (
          <ChartCard
            title="2. Service-wise Performance"
            subtitle="Lead count and conversion by service type"
            type="bar"
            data={servicePerformanceData}
            height={300}
            xKey="name"
            yKeys={['leads', 'converted']}
          />
        )}

        {/* 3. Monthly Lead Trends (Line Chart) */}
        {(selectedChart === 'all' || selectedChart === 'line') && (
          <ChartCard
            title="3. Monthly Lead Trends"
            subtitle="Lead generation trends over the last 12 months"
            type="line"
            data={monthlyChartData}
            height={300}
            xKey="month"
            yKeys={['leads']}
          />
        )}

        {/* 4. Lead Source Analysis (Doughnut Chart) */}
        {(selectedChart === 'all' || selectedChart === 'pie') && (
          <ChartCard
            title="4. Lead Source Analysis"
            subtitle="Where your leads are coming from"
            type="doughnut"
            data={sourceData}
            height={300}
          />
        )}

        {/* 5. Conversion Funnel */}
        {(selectedChart === 'all' || selectedChart === 'bar') && (
          <ChartCard
            title="5. Conversion Funnel"
            subtitle="Lead progression through sales stages"
            type="funnel"
            data={funnelData}
            height={300}
          />
        )}

        {/* 6. Geographic Distribution */}
        {(selectedChart === 'all' || selectedChart === 'bar') && (
          <ChartCard
            title="6. Geographic Distribution"
            subtitle="Lead distribution by region"
            type="horizontalBar"
            data={geoData}
            height={300}
            xKey="region"
            yKeys={['count']}
          />
        )}

        {/* 7. Time-based Activity (Area Chart) */}
        {(selectedChart === 'all' || selectedChart === 'area') && (
          <ChartCard
            title="7. Time-based Activity"
            subtitle="Lead value trends over time"
            type="area"
            data={monthlyChartData}
            height={300}
            xKey="month"
            yKeys={['value']}
          />
        )}

        {/* 8. Performance Metrics (Gauge Charts) */}
        {(selectedChart === 'all') && (
          <ChartCard
            title="8. Performance Metrics"
            subtitle="Key performance indicators"
            type="gauge"
            data={{
              conversionRate: parseFloat((leadData.filter(l => l.status === 'Closed Won').length / leadData.length * 100).toFixed(1)),
              avgDealSize: Math.round(leadData.reduce((sum, l) => sum + l.value, 0) / leadData.length),
              pipelineHealth: 75 // Mock metric
            }}
            height={300}
          />
        )}
      </div>

      {/* Chart Summary */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">Chart Summary & Insights</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="bg-blue-50 p-4 rounded-lg">
            <h4 className="font-semibold text-blue-800">Most Common Status</h4>
            <p className="text-blue-600">
              {Object.entries(statusData_chart).sort(([,a], [,b]) => b - a)[0]?.[0] || 'N/A'}
            </p>
          </div>
          <div className="bg-green-50 p-4 rounded-lg">
            <h4 className="font-semibold text-green-800">Top Lead Source</h4>
            <p className="text-green-600">
              {Object.entries(sourceData).sort(([,a], [,b]) => b - a)[0]?.[0] || 'N/A'}
            </p>
          </div>
          <div className="bg-purple-50 p-4 rounded-lg">
            <h4 className="font-semibold text-purple-800">Best Service</h4>
            <p className="text-purple-600">
              {Object.entries(serviceData).sort(([,a], [,b]) => b - a)[0]?.[0] || 'N/A'}
            </p>
          </div>
          <div className="bg-orange-50 p-4 rounded-lg">
            <h4 className="font-semibold text-orange-800">Top Region</h4>
            <p className="text-orange-600">
              {Object.entries(regionData).sort(([,a], [,b]) => b - a)[0]?.[0] || 'N/A'}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default GraphicalReports;

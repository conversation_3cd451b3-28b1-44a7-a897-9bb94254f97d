{"ast": null, "code": "function _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor);\n  }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  Object.defineProperty(Constructor, \"prototype\", {\n    writable: false\n  });\n  return Constructor;\n}\nfunction _toPropertyKey(arg) {\n  var key = _toPrimitive(arg, \"string\");\n  return typeof key === \"symbol\" ? key : String(key);\n}\nfunction _toPrimitive(input, hint) {\n  if (typeof input !== \"object\" || input === null) return input;\n  var prim = input[Symbol.toPrimitive];\n  if (prim !== undefined) {\n    var res = prim.call(input, hint || \"default\");\n    if (typeof res !== \"object\") return res;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (hint === \"string\" ? String : Number)(input);\n}\nimport { log } from \"../utils/log.js\";\nvar WebSocketClient = /*#__PURE__*/function () {\n  /**\n   * @param {string} url\n   */\n  function WebSocketClient(url) {\n    _classCallCheck(this, WebSocketClient);\n    this.client = new WebSocket(url);\n    this.client.onerror = function (error) {\n      log.error(error);\n    };\n  }\n\n  /**\n   * @param {(...args: any[]) => void} f\n   */\n  _createClass(WebSocketClient, [{\n    key: \"onOpen\",\n    value: function onOpen(f) {\n      this.client.onopen = f;\n    }\n\n    /**\n     * @param {(...args: any[]) => void} f\n     */\n  }, {\n    key: \"onClose\",\n    value: function onClose(f) {\n      this.client.onclose = f;\n    }\n\n    // call f with the message string as the first argument\n    /**\n     * @param {(...args: any[]) => void} f\n     */\n  }, {\n    key: \"onMessage\",\n    value: function onMessage(f) {\n      this.client.onmessage = function (e) {\n        f(e.data);\n      };\n    }\n  }]);\n  return WebSocketClient;\n}();\nexport { WebSocketClient as default };", "map": {"version": 3, "names": ["_classCallCheck", "instance", "<PERSON><PERSON><PERSON><PERSON>", "TypeError", "_defineProperties", "target", "props", "i", "length", "descriptor", "enumerable", "configurable", "writable", "Object", "defineProperty", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "key", "_createClass", "protoProps", "staticProps", "prototype", "arg", "_toPrimitive", "String", "input", "hint", "prim", "Symbol", "toPrimitive", "undefined", "res", "call", "Number", "log", "WebSocketClient", "url", "client", "WebSocket", "onerror", "error", "value", "onOpen", "f", "onopen", "onClose", "onclose", "onMessage", "onmessage", "e", "data", "default"], "sources": ["D:/Augment-projects/DSB_Dashboard/dsb-dashboard/node_modules/webpack-dev-server/client/clients/WebSocketClient.js"], "sourcesContent": ["function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor); } }\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, \"prototype\", { writable: false }); return Constructor; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return typeof key === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (typeof input !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (typeof res !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\nimport { log } from \"../utils/log.js\";\nvar WebSocketClient = /*#__PURE__*/function () {\n  /**\n   * @param {string} url\n   */\n  function WebSocketClient(url) {\n    _classCallCheck(this, WebSocketClient);\n    this.client = new WebSocket(url);\n    this.client.onerror = function (error) {\n      log.error(error);\n    };\n  }\n\n  /**\n   * @param {(...args: any[]) => void} f\n   */\n  _createClass(WebSocketClient, [{\n    key: \"onOpen\",\n    value: function onOpen(f) {\n      this.client.onopen = f;\n    }\n\n    /**\n     * @param {(...args: any[]) => void} f\n     */\n  }, {\n    key: \"onClose\",\n    value: function onClose(f) {\n      this.client.onclose = f;\n    }\n\n    // call f with the message string as the first argument\n    /**\n     * @param {(...args: any[]) => void} f\n     */\n  }, {\n    key: \"onMessage\",\n    value: function onMessage(f) {\n      this.client.onmessage = function (e) {\n        f(e.data);\n      };\n    }\n  }]);\n  return WebSocketClient;\n}();\nexport { WebSocketClient as default };"], "mappings": "AAAA,SAASA,eAAeA,CAACC,QAAQ,EAAEC,WAAW,EAAE;EAAE,IAAI,EAAED,QAAQ,YAAYC,WAAW,CAAC,EAAE;IAAE,MAAM,IAAIC,SAAS,CAAC,mCAAmC,CAAC;EAAE;AAAE;AACxJ,SAASC,iBAAiBA,CAACC,MAAM,EAAEC,KAAK,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,KAAK,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;IAAE,IAAIE,UAAU,GAAGH,KAAK,CAACC,CAAC,CAAC;IAAEE,UAAU,CAACC,UAAU,GAAGD,UAAU,CAACC,UAAU,IAAI,KAAK;IAAED,UAAU,CAACE,YAAY,GAAG,IAAI;IAAE,IAAI,OAAO,IAAIF,UAAU,EAAEA,UAAU,CAACG,QAAQ,GAAG,IAAI;IAAEC,MAAM,CAACC,cAAc,CAACT,MAAM,EAAEU,cAAc,CAACN,UAAU,CAACO,GAAG,CAAC,EAAEP,UAAU,CAAC;EAAE;AAAE;AAC5U,SAASQ,YAAYA,CAACf,WAAW,EAAEgB,UAAU,EAAEC,WAAW,EAAE;EAAE,IAAID,UAAU,EAAEd,iBAAiB,CAACF,WAAW,CAACkB,SAAS,EAAEF,UAAU,CAAC;EAAE,IAAIC,WAAW,EAAEf,iBAAiB,CAACF,WAAW,EAAEiB,WAAW,CAAC;EAAEN,MAAM,CAACC,cAAc,CAACZ,WAAW,EAAE,WAAW,EAAE;IAAEU,QAAQ,EAAE;EAAM,CAAC,CAAC;EAAE,OAAOV,WAAW;AAAE;AAC5R,SAASa,cAAcA,CAACM,GAAG,EAAE;EAAE,IAAIL,GAAG,GAAGM,YAAY,CAACD,GAAG,EAAE,QAAQ,CAAC;EAAE,OAAO,OAAOL,GAAG,KAAK,QAAQ,GAAGA,GAAG,GAAGO,MAAM,CAACP,GAAG,CAAC;AAAE;AAC1H,SAASM,YAAYA,CAACE,KAAK,EAAEC,IAAI,EAAE;EAAE,IAAI,OAAOD,KAAK,KAAK,QAAQ,IAAIA,KAAK,KAAK,IAAI,EAAE,OAAOA,KAAK;EAAE,IAAIE,IAAI,GAAGF,KAAK,CAACG,MAAM,CAACC,WAAW,CAAC;EAAE,IAAIF,IAAI,KAAKG,SAAS,EAAE;IAAE,IAAIC,GAAG,GAAGJ,IAAI,CAACK,IAAI,CAACP,KAAK,EAAEC,IAAI,IAAI,SAAS,CAAC;IAAE,IAAI,OAAOK,GAAG,KAAK,QAAQ,EAAE,OAAOA,GAAG;IAAE,MAAM,IAAI3B,SAAS,CAAC,8CAA8C,CAAC;EAAE;EAAE,OAAO,CAACsB,IAAI,KAAK,QAAQ,GAAGF,MAAM,GAAGS,MAAM,EAAER,KAAK,CAAC;AAAE;AACxX,SAASS,GAAG,QAAQ,iBAAiB;AACrC,IAAIC,eAAe,GAAG,aAAa,YAAY;EAC7C;AACF;AACA;EACE,SAASA,eAAeA,CAACC,GAAG,EAAE;IAC5BnC,eAAe,CAAC,IAAI,EAAEkC,eAAe,CAAC;IACtC,IAAI,CAACE,MAAM,GAAG,IAAIC,SAAS,CAACF,GAAG,CAAC;IAChC,IAAI,CAACC,MAAM,CAACE,OAAO,GAAG,UAAUC,KAAK,EAAE;MACrCN,GAAG,CAACM,KAAK,CAACA,KAAK,CAAC;IAClB,CAAC;EACH;;EAEA;AACF;AACA;EACEtB,YAAY,CAACiB,eAAe,EAAE,CAAC;IAC7BlB,GAAG,EAAE,QAAQ;IACbwB,KAAK,EAAE,SAASC,MAAMA,CAACC,CAAC,EAAE;MACxB,IAAI,CAACN,MAAM,CAACO,MAAM,GAAGD,CAAC;IACxB;;IAEA;AACJ;AACA;EACE,CAAC,EAAE;IACD1B,GAAG,EAAE,SAAS;IACdwB,KAAK,EAAE,SAASI,OAAOA,CAACF,CAAC,EAAE;MACzB,IAAI,CAACN,MAAM,CAACS,OAAO,GAAGH,CAAC;IACzB;;IAEA;IACA;AACJ;AACA;EACE,CAAC,EAAE;IACD1B,GAAG,EAAE,WAAW;IAChBwB,KAAK,EAAE,SAASM,SAASA,CAACJ,CAAC,EAAE;MAC3B,IAAI,CAACN,MAAM,CAACW,SAAS,GAAG,UAAUC,CAAC,EAAE;QACnCN,CAAC,CAACM,CAAC,CAACC,IAAI,CAAC;MACX,CAAC;IACH;EACF,CAAC,CAAC,CAAC;EACH,OAAOf,eAAe;AACxB,CAAC,CAAC,CAAC;AACH,SAASA,eAAe,IAAIgB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
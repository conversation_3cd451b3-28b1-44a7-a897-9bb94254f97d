import React from 'react';
import {
  <PERSON><PERSON><PERSON>,
  Pie,
  Cell,
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer
} from 'recharts';

const QuickChart = ({ type, data, title }) => {
  const COLORS = ['#3B82F6', '#10B981', '#8B5CF6', '#F59E0B', '#EF4444', '#6B7280'];

  const renderPieChart = () => {
    const chartData = Object.entries(data).map(([key, value]) => ({
      name: key,
      value: value
    }));

    return (
      <ResponsiveContainer width="100%" height={250}>
        <PieChart>
          <Pie
            data={chartData}
            cx="50%"
            cy="50%"
            labelLine={false}
            label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
            outerRadius={80}
            fill="#8884d8"
            dataKey="value"
          >
            {chartData.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
            ))}
          </Pie>
          <Tooltip />
        </PieChart>
      </ResponsiveContainer>
    );
  };

  const renderLineChart = () => {
    // Group data by month for line chart
    const monthlyData = {};
    data.forEach(item => {
      const month = item.createdDate ? item.createdDate.substring(0, 7) : '2024-01';
      monthlyData[month] = (monthlyData[month] || 0) + 1;
    });

    const chartData = Object.entries(monthlyData)
      .sort()
      .slice(-6) // Last 6 months
      .map(([month, count]) => ({
        month: month.substring(5), // Just MM
        leads: count
      }));

    return (
      <ResponsiveContainer width="100%" height={250}>
        <LineChart data={chartData}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="month" />
          <YAxis />
          <Tooltip />
          <Line 
            type="monotone" 
            dataKey="leads" 
            stroke="#3B82F6" 
            strokeWidth={2}
            dot={{ fill: '#3B82F6', strokeWidth: 2, r: 4 }}
          />
        </LineChart>
      </ResponsiveContainer>
    );
  };

  return (
    <div className="chart-animate">
      {type === 'pie' ? renderPieChart() : renderLineChart()}
    </div>
  );
};

export default QuickChart;

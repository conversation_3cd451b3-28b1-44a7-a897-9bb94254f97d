import React, { useState } from 'react';
import { useData } from '../context/DataContext';
import AnalysisCard from '../components/Analysis/AnalysisCard';
import { 
  FiTrendingUp, 
  FiTarget, 
  FiBarChart3, 
  FiActivity, 
  FiUsers, 
  FiDollarSign, 
  <PERSON>Pie<PERSON>hart,
  FiFilter
} from 'react-icons/fi';

const AnalysisReports = () => {
  const { leadData, statusData, loading } = useData();
  const [selectedAnalysis, setSelectedAnalysis] = useState('all');
  const [dateRange, setDateRange] = useState('all');

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  const analysisTypes = [
    { id: 'all', name: 'All Analysis', icon: FiBarChart3 },
    { id: 'conversion', name: 'Conversion', icon: FiTarget },
    { id: 'performance', name: 'Performance', icon: FiTrendingUp },
    { id: 'trends', name: 'Trends', icon: FiActivity },
    { id: 'predictive', name: 'Predictive', icon: FiUsers }
  ];

  // Analysis 1: Lead Conversion Analysis
  const conversionAnalysis = () => {
    const totalLeads = leadData.length;
    const convertedLeads = leadData.filter(lead => lead.status === 'Closed Won').length;
    const conversionRate = ((convertedLeads / totalLeads) * 100).toFixed(2);
    
    const stageConversion = {
      'New to Contacted': ((leadData.filter(l => l.status !== 'New').length / totalLeads) * 100).toFixed(1),
      'Contacted to Qualified': ((leadData.filter(l => ['Qualified', 'Proposal', 'Negotiation', 'Closed Won'].includes(l.status)).length / leadData.filter(l => l.status !== 'New').length) * 100).toFixed(1),
      'Qualified to Proposal': ((leadData.filter(l => ['Proposal', 'Negotiation', 'Closed Won'].includes(l.status)).length / leadData.filter(l => ['Qualified', 'Proposal', 'Negotiation', 'Closed Won'].includes(l.status)).length) * 100).toFixed(1),
      'Proposal to Closed': ((convertedLeads / leadData.filter(l => ['Proposal', 'Negotiation', 'Closed Won'].includes(l.status)).length) * 100).toFixed(1)
    };

    return {
      totalLeads,
      convertedLeads,
      conversionRate,
      stageConversion,
      insights: [
        `Overall conversion rate is ${conversionRate}%`,
        `${convertedLeads} deals successfully closed`,
        `Highest drop-off at ${Object.entries(stageConversion).sort(([,a], [,b]) => a - b)[0][0]} stage`
      ]
    };
  };

  // Analysis 2: Service Performance Analytics
  const servicePerformanceAnalysis = () => {
    const serviceMetrics = statusData.map(service => ({
      ...service,
      efficiency: (service.convertedLeads / service.totalLeads * 100).toFixed(1),
      revenuePerLead: (service.revenue / service.totalLeads).toFixed(0)
    }));

    const topService = serviceMetrics.sort((a, b) => b.efficiency - a.efficiency)[0];
    const totalRevenue = serviceMetrics.reduce((sum, s) => sum + s.revenue, 0);

    return {
      serviceMetrics,
      topService,
      totalRevenue,
      insights: [
        `${topService.service} has the highest conversion rate at ${topService.efficiency}%`,
        `Total revenue across all services: $${(totalRevenue/1000000).toFixed(1)}M`,
        `Average revenue per lead: $${(totalRevenue / leadData.length).toFixed(0)}`
      ]
    };
  };

  // Analysis 3: Trend Analysis
  const trendAnalysis = () => {
    const monthlyTrends = {};
    leadData.forEach(lead => {
      const month = lead.createdDate.substring(0, 7);
      if (!monthlyTrends[month]) {
        monthlyTrends[month] = { leads: 0, value: 0, converted: 0 };
      }
      monthlyTrends[month].leads += 1;
      monthlyTrends[month].value += lead.value;
      if (lead.status === 'Closed Won') monthlyTrends[month].converted += 1;
    });

    const sortedMonths = Object.keys(monthlyTrends).sort();
    const latestMonth = sortedMonths[sortedMonths.length - 1];
    const previousMonth = sortedMonths[sortedMonths.length - 2];

    const growth = previousMonth ? 
      (((monthlyTrends[latestMonth].leads - monthlyTrends[previousMonth].leads) / monthlyTrends[previousMonth].leads) * 100).toFixed(1) : 0;

    return {
      monthlyTrends,
      growth,
      latestMonth,
      insights: [
        `${growth > 0 ? 'Growth' : 'Decline'} of ${Math.abs(growth)}% in lead generation`,
        `${monthlyTrends[latestMonth]?.leads || 0} leads generated in latest month`,
        `Trend shows ${growth > 5 ? 'strong positive' : growth > 0 ? 'positive' : 'negative'} momentum`
      ]
    };
  };

  // Analysis 4: Comparative Analysis
  const comparativeAnalysis = () => {
    const sourceComparison = {};
    const regionComparison = {};

    leadData.forEach(lead => {
      // Source comparison
      if (!sourceComparison[lead.source]) {
        sourceComparison[lead.source] = { total: 0, converted: 0, value: 0 };
      }
      sourceComparison[lead.source].total += 1;
      sourceComparison[lead.source].value += lead.value;
      if (lead.status === 'Closed Won') sourceComparison[lead.source].converted += 1;

      // Region comparison
      if (!regionComparison[lead.region]) {
        regionComparison[lead.region] = { total: 0, converted: 0, value: 0 };
      }
      regionComparison[lead.region].total += 1;
      regionComparison[lead.region].value += lead.value;
      if (lead.status === 'Closed Won') regionComparison[lead.region].converted += 1;
    });

    const bestSource = Object.entries(sourceComparison)
      .map(([source, data]) => ({ source, ...data, rate: (data.converted / data.total * 100).toFixed(1) }))
      .sort((a, b) => b.rate - a.rate)[0];

    const bestRegion = Object.entries(regionComparison)
      .map(([region, data]) => ({ region, ...data, rate: (data.converted / data.total * 100).toFixed(1) }))
      .sort((a, b) => b.rate - a.rate)[0];

    return {
      sourceComparison,
      regionComparison,
      bestSource,
      bestRegion,
      insights: [
        `${bestSource.source} is the most effective lead source (${bestSource.rate}% conversion)`,
        `${bestRegion.region} region shows highest performance (${bestRegion.rate}% conversion)`,
        `Focus marketing efforts on top-performing channels`
      ]
    };
  };

  // Analysis 5: Predictive Analytics
  const predictiveAnalysis = () => {
    const currentMonth = new Date().getMonth();
    const currentYear = new Date().getFullYear();
    
    // Calculate average monthly leads for prediction
    const monthlyAverages = {};
    leadData.forEach(lead => {
      const leadDate = new Date(lead.createdDate);
      const month = leadDate.getMonth();
      monthlyAverages[month] = (monthlyAverages[month] || 0) + 1;
    });

    const avgLeadsPerMonth = Object.values(monthlyAverages).reduce((sum, count) => sum + count, 0) / Object.keys(monthlyAverages).length;
    const predictedNextMonth = Math.round(avgLeadsPerMonth * 1.1); // 10% growth assumption
    const predictedRevenue = predictedNextMonth * (leadData.reduce((sum, l) => sum + l.value, 0) / leadData.length);

    return {
      avgLeadsPerMonth: Math.round(avgLeadsPerMonth),
      predictedNextMonth,
      predictedRevenue,
      insights: [
        `Predicted ${predictedNextMonth} leads next month`,
        `Estimated revenue: $${(predictedRevenue/1000).toFixed(0)}K`,
        `Based on historical trends and growth patterns`
      ]
    };
  };

  // Analysis 6: ROI Analysis
  const roiAnalysis = () => {
    const totalRevenue = leadData.filter(l => l.status === 'Closed Won').reduce((sum, l) => sum + l.value, 0);
    const estimatedCost = leadData.length * 500; // Assume $500 cost per lead
    const roi = ((totalRevenue - estimatedCost) / estimatedCost * 100).toFixed(1);
    
    const serviceROI = statusData.map(service => {
      const serviceCost = service.totalLeads * 500;
      const serviceROI = ((service.revenue - serviceCost) / serviceCost * 100).toFixed(1);
      return { ...service, roi: serviceROI, cost: serviceCost };
    });

    return {
      totalRevenue,
      estimatedCost,
      roi,
      serviceROI,
      insights: [
        `Overall ROI: ${roi}%`,
        `Total revenue generated: $${(totalRevenue/1000000).toFixed(1)}M`,
        `Most profitable service: ${serviceROI.sort((a, b) => b.roi - a.roi)[0].service}`
      ]
    };
  };

  // Analysis 7: Customer Segmentation
  const segmentationAnalysis = () => {
    const segments = {
      'High Value': leadData.filter(l => l.value > 50000),
      'Medium Value': leadData.filter(l => l.value >= 20000 && l.value <= 50000),
      'Low Value': leadData.filter(l => l.value < 20000)
    };

    const segmentMetrics = Object.entries(segments).map(([segment, leads]) => ({
      segment,
      count: leads.length,
      avgValue: leads.length > 0 ? (leads.reduce((sum, l) => sum + l.value, 0) / leads.length).toFixed(0) : 0,
      conversionRate: leads.length > 0 ? ((leads.filter(l => l.status === 'Closed Won').length / leads.length) * 100).toFixed(1) : 0
    }));

    return {
      segments,
      segmentMetrics,
      insights: [
        `${segments['High Value'].length} high-value prospects identified`,
        `High-value segment shows ${segmentMetrics[0].conversionRate}% conversion rate`,
        `Focus on nurturing medium-value prospects for growth`
      ]
    };
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="dashboard-header">
        <h1 className="text-3xl font-bold mb-2">Analysis Reports</h1>
        <p className="text-blue-100">
          7 Comprehensive analytics for data-driven decision making
        </p>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg shadow-sm p-4">
        <div className="flex flex-wrap gap-4 items-center">
          <div className="flex items-center space-x-2">
            <FiFilter className="text-gray-400" size={16} />
            <span className="text-sm font-medium text-gray-700">Filter by:</span>
          </div>
          
          <div className="flex flex-wrap gap-2">
            {analysisTypes.map((type) => (
              <button
                key={type.id}
                onClick={() => setSelectedAnalysis(type.id)}
                className={`flex items-center space-x-2 px-3 py-1 rounded-lg text-sm transition-colors ${
                  selectedAnalysis === type.id
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                <type.icon size={14} />
                <span>{type.name}</span>
              </button>
            ))}
          </div>

          <select 
            value={dateRange} 
            onChange={(e) => setDateRange(e.target.value)}
            className="px-3 py-1 border border-gray-300 rounded-lg text-sm"
          >
            <option value="all">All Time</option>
            <option value="ytd">Year to Date</option>
            <option value="last6">Last 6 Months</option>
            <option value="last3">Last 3 Months</option>
          </select>
        </div>
      </div>

      {/* Analysis Cards */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 1. Lead Conversion Analysis */}
        {(selectedAnalysis === 'all' || selectedAnalysis === 'conversion') && (
          <AnalysisCard
            title="1. Lead Conversion Analysis"
            subtitle="Analyze conversion rates across sales stages"
            icon={FiTarget}
            data={conversionAnalysis()}
            type="conversion"
          />
        )}

        {/* 2. Service Performance Analytics */}
        {(selectedAnalysis === 'all' || selectedAnalysis === 'performance') && (
          <AnalysisCard
            title="2. Service Performance Analytics"
            subtitle="Compare performance across different services"
            icon={FiBarChart3}
            data={servicePerformanceAnalysis()}
            type="performance"
          />
        )}

        {/* 3. Trend Analysis */}
        {(selectedAnalysis === 'all' || selectedAnalysis === 'trends') && (
          <AnalysisCard
            title="3. Trend Analysis Dashboard"
            subtitle="Identify patterns and trends over time"
            icon={FiTrendingUp}
            data={trendAnalysis()}
            type="trends"
          />
        )}

        {/* 4. Comparative Analysis */}
        {(selectedAnalysis === 'all') && (
          <AnalysisCard
            title="4. Comparative Analysis"
            subtitle="Compare performance across sources and regions"
            icon={FiActivity}
            data={comparativeAnalysis()}
            type="comparative"
          />
        )}

        {/* 5. Predictive Analytics */}
        {(selectedAnalysis === 'all' || selectedAnalysis === 'predictive') && (
          <AnalysisCard
            title="5. Predictive Analytics"
            subtitle="Forecast future performance and trends"
            icon={FiUsers}
            data={predictiveAnalysis()}
            type="predictive"
          />
        )}

        {/* 6. ROI Analysis */}
        {(selectedAnalysis === 'all') && (
          <AnalysisCard
            title="6. ROI Analysis"
            subtitle="Return on investment across services"
            icon={FiDollarSign}
            data={roiAnalysis()}
            type="roi"
          />
        )}

        {/* 7. Customer Segmentation */}
        {(selectedAnalysis === 'all') && (
          <AnalysisCard
            title="7. Customer Segmentation Analysis"
            subtitle="Segment customers by value and behavior"
            icon={FiPieChart}
            data={segmentationAnalysis()}
            type="segmentation"
          />
        )}
      </div>
    </div>
  );
};

export default AnalysisReports;

import React from 'react';
import { FiTrendingUp, FiDollarSign, FiTarget } from 'react-icons/fi';

const TopPerformers = ({ data }) => {
  // Sort by conversion rate
  const sortedData = [...data].sort((a, b) => parseFloat(b.conversionRate) - parseFloat(a.conversionRate));
  const topPerformers = sortedData.slice(0, 5);

  return (
    <div className="dashboard-card">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-800">Top Performing Services</h3>
        <FiTrendingUp className="text-gray-400" size={20} />
      </div>
      
      <div className="space-y-4">
        {topPerformers.map((service, index) => (
          <div key={service.id} className="relative">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center space-x-2">
                <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold text-white ${
                  index === 0 ? 'bg-yellow-500' : 
                  index === 1 ? 'bg-gray-400' : 
                  index === 2 ? 'bg-orange-500' : 'bg-blue-500'
                }`}>
                  {index + 1}
                </div>
                <span className="text-sm font-medium text-gray-900">{service.service}</span>
              </div>
              <span className="text-sm font-bold text-green-600">{service.conversionRate}%</span>
            </div>
            
            <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
              <div 
                className="bg-blue-600 h-2 rounded-full transition-all duration-500"
                style={{ width: `${service.conversionRate}%` }}
              ></div>
            </div>
            
            <div className="flex items-center justify-between text-xs text-gray-500">
              <div className="flex items-center">
                <FiTarget size={12} className="mr-1" />
                {service.totalLeads} leads
              </div>
              <div className="flex items-center">
                <FiDollarSign size={12} className="mr-1" />
                ${(service.revenue / 1000).toFixed(0)}K
              </div>
            </div>
          </div>
        ))}
      </div>
      
      <div className="mt-6 p-4 bg-blue-50 rounded-lg">
        <h4 className="text-sm font-semibold text-blue-800 mb-2">Performance Insights</h4>
        <ul className="text-xs text-blue-700 space-y-1">
          <li>• {topPerformers[0]?.service} leads the conversion rate</li>
          <li>• Average deal size: ${Math.round(topPerformers.reduce((sum, s) => sum + s.avgDealSize, 0) / topPerformers.length).toLocaleString()}</li>
          <li>• Total pipeline value: ${(topPerformers.reduce((sum, s) => sum + s.revenue, 0) / 1000000).toFixed(1)}M</li>
        </ul>
      </div>
    </div>
  );
};

export default TopPerformers;

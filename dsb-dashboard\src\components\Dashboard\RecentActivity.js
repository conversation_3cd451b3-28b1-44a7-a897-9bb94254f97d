import React from 'react';
import { FiUser, FiDollarSign, FiCalendar, FiArrowRight } from 'react-icons/fi';

const RecentActivity = ({ leads }) => {
  const getStatusColor = (status) => {
    const colors = {
      'New': 'bg-blue-100 text-blue-800',
      'Contacted': 'bg-yellow-100 text-yellow-800',
      'Qualified': 'bg-green-100 text-green-800',
      'Proposal': 'bg-purple-100 text-purple-800',
      'Negotiation': 'bg-orange-100 text-orange-800',
      'Closed Won': 'bg-green-100 text-green-800',
      'Closed Lost': 'bg-red-100 text-red-800'
    };
    return colors[status] || 'bg-gray-100 text-gray-800';
  };

  return (
    <div className="dashboard-card">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-800">Recent Activity</h3>
        <button className="text-blue-600 hover:text-blue-800 text-sm font-medium flex items-center">
          View All
          <FiArrowRight className="ml-1" size={14} />
        </button>
      </div>
      
      <div className="space-y-4">
        {leads.map((lead, index) => (
          <div key={lead.id} className="flex items-center space-x-4 p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
            <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center">
              <FiUser className="text-white" size={16} />
            </div>
            
            <div className="flex-1 min-w-0">
              <div className="flex items-center justify-between">
                <p className="text-sm font-medium text-gray-900 truncate">
                  {lead.company}
                </p>
                <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(lead.status)}`}>
                  {lead.status}
                </span>
              </div>
              
              <div className="flex items-center space-x-4 mt-1">
                <div className="flex items-center text-xs text-gray-500">
                  <FiDollarSign size={12} className="mr-1" />
                  ${lead.value.toLocaleString()}
                </div>
                <div className="flex items-center text-xs text-gray-500">
                  <FiCalendar size={12} className="mr-1" />
                  {lead.lastActivity}
                </div>
                <div className="text-xs text-gray-500">
                  {lead.assignedTo}
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
      
      {leads.length === 0 && (
        <div className="text-center py-8 text-gray-500">
          <FiUser size={48} className="mx-auto mb-4 text-gray-300" />
          <p>No recent activity</p>
        </div>
      )}
    </div>
  );
};

export default RecentActivity;

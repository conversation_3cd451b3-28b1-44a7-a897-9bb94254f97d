import React from 'react';
import { FiTrendingUp, FiTrendingDown, FiInfo } from 'react-icons/fi';

const AnalysisCard = ({ title, subtitle, icon: Icon, data, type }) => {
  const renderConversionAnalysis = () => (
    <div className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <div className="bg-blue-50 p-3 rounded-lg">
          <p className="text-sm text-blue-600 font-medium">Total Leads</p>
          <p className="text-2xl font-bold text-blue-800">{data.totalLeads}</p>
        </div>
        <div className="bg-green-50 p-3 rounded-lg">
          <p className="text-sm text-green-600 font-medium">Converted</p>
          <p className="text-2xl font-bold text-green-800">{data.convertedLeads}</p>
        </div>
      </div>
      
      <div className="bg-purple-50 p-3 rounded-lg text-center">
        <p className="text-sm text-purple-600 font-medium">Conversion Rate</p>
        <p className="text-3xl font-bold text-purple-800">{data.conversionRate}%</p>
      </div>

      <div className="space-y-2">
        <h4 className="font-medium text-gray-800">Stage Conversion Rates</h4>
        {Object.entries(data.stageConversion).map(([stage, rate]) => (
          <div key={stage} className="flex justify-between items-center">
            <span className="text-sm text-gray-600">{stage}</span>
            <span className="text-sm font-medium text-gray-800">{rate}%</span>
          </div>
        ))}
      </div>
    </div>
  );

  const renderPerformanceAnalysis = () => (
    <div className="space-y-4">
      <div className="bg-gradient-to-r from-green-500 to-green-600 text-white p-4 rounded-lg">
        <p className="text-sm opacity-90">Total Revenue</p>
        <p className="text-2xl font-bold">${(data.totalRevenue / 1000000).toFixed(1)}M</p>
      </div>

      <div className="space-y-2">
        <h4 className="font-medium text-gray-800">Top Performing Service</h4>
        <div className="bg-yellow-50 p-3 rounded-lg">
          <p className="font-medium text-yellow-800">{data.topService.service}</p>
          <p className="text-sm text-yellow-600">{data.topService.efficiency}% conversion rate</p>
        </div>
      </div>

      <div className="space-y-2">
        <h4 className="font-medium text-gray-800">Service Metrics</h4>
        <div className="max-h-32 overflow-y-auto space-y-1">
          {data.serviceMetrics.slice(0, 3).map((service, index) => (
            <div key={index} className="flex justify-between text-sm">
              <span className="text-gray-600">{service.service}</span>
              <span className="font-medium">{service.efficiency}%</span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  const renderTrendsAnalysis = () => (
    <div className="space-y-4">
      <div className="flex items-center justify-center">
        <div className={`flex items-center space-x-2 px-4 py-2 rounded-lg ${
          parseFloat(data.growth) > 0 ? 'bg-green-50 text-green-800' : 'bg-red-50 text-red-800'
        }`}>
          {parseFloat(data.growth) > 0 ? <FiTrendingUp /> : <FiTrendingDown />}
          <span className="font-bold">{Math.abs(data.growth)}% {parseFloat(data.growth) > 0 ? 'Growth' : 'Decline'}</span>
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div className="bg-blue-50 p-3 rounded-lg text-center">
          <p className="text-sm text-blue-600">Latest Month</p>
          <p className="text-xl font-bold text-blue-800">{data.monthlyTrends[data.latestMonth]?.leads || 0}</p>
          <p className="text-xs text-blue-600">leads</p>
        </div>
        <div className="bg-purple-50 p-3 rounded-lg text-center">
          <p className="text-sm text-purple-600">Trend</p>
          <p className="text-xl font-bold text-purple-800">
            {parseFloat(data.growth) > 5 ? 'Strong' : parseFloat(data.growth) > 0 ? 'Positive' : 'Negative'}
          </p>
          <p className="text-xs text-purple-600">momentum</p>
        </div>
      </div>
    </div>
  );

  const renderComparativeAnalysis = () => (
    <div className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <div className="bg-blue-50 p-3 rounded-lg">
          <p className="text-sm text-blue-600 font-medium">Best Source</p>
          <p className="font-bold text-blue-800">{data.bestSource.source}</p>
          <p className="text-xs text-blue-600">{data.bestSource.rate}% conversion</p>
        </div>
        <div className="bg-green-50 p-3 rounded-lg">
          <p className="text-sm text-green-600 font-medium">Best Region</p>
          <p className="font-bold text-green-800">{data.bestRegion.region}</p>
          <p className="text-xs text-green-600">{data.bestRegion.rate}% conversion</p>
        </div>
      </div>

      <div className="space-y-2">
        <h4 className="font-medium text-gray-800">Source Performance</h4>
        <div className="space-y-1">
          {Object.entries(data.sourceComparison).slice(0, 3).map(([source, metrics]) => (
            <div key={source} className="flex justify-between text-sm">
              <span className="text-gray-600">{source}</span>
              <span className="font-medium">{((metrics.converted / metrics.total) * 100).toFixed(1)}%</span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  const renderPredictiveAnalysis = () => (
    <div className="space-y-4">
      <div className="bg-gradient-to-r from-purple-500 to-purple-600 text-white p-4 rounded-lg text-center">
        <p className="text-sm opacity-90">Predicted Next Month</p>
        <p className="text-3xl font-bold">{data.predictedNextMonth}</p>
        <p className="text-sm opacity-90">leads</p>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div className="bg-blue-50 p-3 rounded-lg text-center">
          <p className="text-sm text-blue-600">Avg/Month</p>
          <p className="text-xl font-bold text-blue-800">{data.avgLeadsPerMonth}</p>
        </div>
        <div className="bg-green-50 p-3 rounded-lg text-center">
          <p className="text-sm text-green-600">Est. Revenue</p>
          <p className="text-xl font-bold text-green-800">${(data.predictedRevenue/1000).toFixed(0)}K</p>
        </div>
      </div>

      <div className="bg-yellow-50 p-3 rounded-lg">
        <div className="flex items-center space-x-2">
          <FiInfo className="text-yellow-600" size={16} />
          <p className="text-sm text-yellow-800">Based on historical trends</p>
        </div>
      </div>
    </div>
  );

  const renderROIAnalysis = () => (
    <div className="space-y-4">
      <div className="bg-gradient-to-r from-green-500 to-green-600 text-white p-4 rounded-lg text-center">
        <p className="text-sm opacity-90">Overall ROI</p>
        <p className="text-3xl font-bold">{data.roi}%</p>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div className="bg-blue-50 p-3 rounded-lg text-center">
          <p className="text-sm text-blue-600">Revenue</p>
          <p className="text-lg font-bold text-blue-800">${(data.totalRevenue/1000000).toFixed(1)}M</p>
        </div>
        <div className="bg-red-50 p-3 rounded-lg text-center">
          <p className="text-sm text-red-600">Cost</p>
          <p className="text-lg font-bold text-red-800">${(data.estimatedCost/1000).toFixed(0)}K</p>
        </div>
      </div>

      <div className="space-y-2">
        <h4 className="font-medium text-gray-800">Service ROI</h4>
        <div className="space-y-1">
          {data.serviceROI.slice(0, 3).map((service, index) => (
            <div key={index} className="flex justify-between text-sm">
              <span className="text-gray-600">{service.service}</span>
              <span className={`font-medium ${parseFloat(service.roi) > 0 ? 'text-green-600' : 'text-red-600'}`}>
                {service.roi}%
              </span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  const renderSegmentationAnalysis = () => (
    <div className="space-y-4">
      <div className="grid grid-cols-3 gap-2">
        {data.segmentMetrics.map((segment, index) => (
          <div key={segment.segment} className={`p-3 rounded-lg text-center ${
            index === 0 ? 'bg-red-50' : index === 1 ? 'bg-yellow-50' : 'bg-green-50'
          }`}>
            <p className={`text-xs font-medium ${
              index === 0 ? 'text-red-600' : index === 1 ? 'text-yellow-600' : 'text-green-600'
            }`}>
              {segment.segment}
            </p>
            <p className={`text-lg font-bold ${
              index === 0 ? 'text-red-800' : index === 1 ? 'text-yellow-800' : 'text-green-800'
            }`}>
              {segment.count}
            </p>
            <p className={`text-xs ${
              index === 0 ? 'text-red-600' : index === 1 ? 'text-yellow-600' : 'text-green-600'
            }`}>
              {segment.conversionRate}% conv.
            </p>
          </div>
        ))}
      </div>

      <div className="space-y-2">
        <h4 className="font-medium text-gray-800">Segment Details</h4>
        {data.segmentMetrics.map((segment, index) => (
          <div key={segment.segment} className="flex justify-between text-sm">
            <span className="text-gray-600">{segment.segment}</span>
            <span className="font-medium">${parseInt(segment.avgValue).toLocaleString()}</span>
          </div>
        ))}
      </div>
    </div>
  );

  const renderAnalysisContent = () => {
    switch (type) {
      case 'conversion':
        return renderConversionAnalysis();
      case 'performance':
        return renderPerformanceAnalysis();
      case 'trends':
        return renderTrendsAnalysis();
      case 'comparative':
        return renderComparativeAnalysis();
      case 'predictive':
        return renderPredictiveAnalysis();
      case 'roi':
        return renderROIAnalysis();
      case 'segmentation':
        return renderSegmentationAnalysis();
      default:
        return <div>Analysis type not supported</div>;
    }
  };

  return (
    <div className="dashboard-card fade-in">
      <div className="flex items-center space-x-3 mb-4">
        <div className="p-2 bg-blue-100 rounded-lg">
          <Icon className="text-blue-600" size={20} />
        </div>
        <div>
          <h3 className="text-lg font-semibold text-gray-800">{title}</h3>
          <p className="text-sm text-gray-600">{subtitle}</p>
        </div>
      </div>

      {renderAnalysisContent()}

      {/* Insights */}
      {data.insights && (
        <div className="mt-4 p-3 bg-gray-50 rounded-lg">
          <h4 className="text-sm font-medium text-gray-800 mb-2">Key Insights</h4>
          <ul className="space-y-1">
            {data.insights.map((insight, index) => (
              <li key={index} className="text-xs text-gray-600 flex items-start">
                <span className="text-blue-500 mr-2">•</span>
                {insight}
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
};

export default AnalysisCard;

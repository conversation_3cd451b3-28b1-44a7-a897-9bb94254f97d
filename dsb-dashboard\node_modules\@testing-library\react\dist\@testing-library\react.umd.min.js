!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("react"),require("react-dom/test-utils"),require("react-dom"),require("react-dom/client"),require("@testing-library/dom")):"function"==typeof define&&define.amd?define(["exports","react","react-dom/test-utils","react-dom","react-dom/client","@testing-library/dom"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).TestingLibraryReact={},e.<PERSON>act,e.<PERSON>act<PERSON>est<PERSON>,e.ReactDOM,e.ReactDOMClient,e.dom)}(this,(function(e,t,r,n,o,c){"use strict";function u(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function a(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var n=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,n.get?n:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}var i=a(t),f=a(r),l=u(n),s=a(o);const d="function"==typeof i.act?i.act:f.act;function p(){if("undefined"!=typeof globalThis)return globalThis;if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;if("undefined"!=typeof global)return global;throw new Error("unable to locate global object")}function y(e){p().IS_REACT_ACT_ENVIRONMENT=e}function m(){return p().IS_REACT_ACT_ENVIRONMENT}const h=(E=d,e=>{const t=m();y(!0);try{let r=!1;const n=E((()=>{const t=e();return null!==t&&"object"==typeof t&&"function"==typeof t.then&&(r=!0),t}));return r?{then:(e,r)=>{n.then((r=>{y(t),e(r)}),(e=>{y(t),r(e)}))}}:(y(t),n)}catch(e){throw y(t),e}});var E;const b=function(){return c.fireEvent(...arguments)};Object.keys(c.fireEvent).forEach((e=>{b[e]=function(){return c.fireEvent[e](...arguments)}}));const g=b.mouseEnter,v=b.mouseLeave;b.mouseEnter=function(){return g(...arguments),b.mouseOver(...arguments)},b.mouseLeave=function(){return v(...arguments),b.mouseOut(...arguments)};const w=b.pointerEnter,R=b.pointerLeave;b.pointerEnter=function(){return w(...arguments),b.pointerOver(...arguments)},b.pointerLeave=function(){return R(...arguments),b.pointerOut(...arguments)};const T=b.select;b.select=(e,t)=>{T(e,t),e.focus(),b.keyUp(e,t)};const O=b.blur,M=b.focus;b.blur=function(){return b.focusOut(...arguments),O(...arguments)},b.focus=function(){return b.focusIn(...arguments),M(...arguments)};let C={reactStrictMode:!1};function j(){return{...c.getConfig(),...C}}c.configure({unstable_advanceTimersWrapper:e=>h(e),asyncWrapper:async e=>{const t=m();y(!1);try{const t=await e();return await new Promise((e=>{setTimeout((()=>{e()}),0),"undefined"==typeof jest||null===jest||!0!==setTimeout._isMockFunction&&!Object.prototype.hasOwnProperty.call(setTimeout,"clock")||jest.advanceTimersByTime(0)})),t}finally{y(t)}},eventWrapper:e=>{let t;return h((()=>{t=e()})),t}});const S=new Set,_=[];function A(e,t){return t??j().reactStrictMode?i.createElement(i.StrictMode,null,e):e}function P(e,t){return t?i.createElement(t,null,e):e}function k(e,t){let r,{hydrate:n,onCaughtError:o,onRecoverableError:c,ui:u,wrapper:a,reactStrictMode:i}=t;return n?h((()=>{r=s.hydrateRoot(e,A(P(u,a),i),{onCaughtError:o,onRecoverableError:c})})):r=s.createRoot(e,{onCaughtError:o,onRecoverableError:c}),{hydrate(){if(!n)throw new Error("Attempted to hydrate a non-hydrateable root. This is a bug in `@testing-library/react`.")},render(e){r.render(e)},unmount(){r.unmount()}}}function I(e){return{hydrate(t){l.default.hydrate(t,e)},render(t){l.default.render(t,e)},unmount(){l.default.unmountComponentAtNode(e)}}}function L(e,t){let{baseElement:r,container:n,hydrate:o,queries:u,root:a,wrapper:i,reactStrictMode:f}=t;return h((()=>{o?a.hydrate(A(P(e,i),f),n):a.render(A(P(e,i),f),n)})),{container:n,baseElement:r,debug:function(e,t,n){return void 0===e&&(e=r),Array.isArray(e)?e.forEach((e=>console.log(c.prettyDOM(e,t,n)))):console.log(c.prettyDOM(e,t,n))},unmount:()=>{h((()=>{a.unmount()}))},rerender:e=>{L(e,{container:n,baseElement:r,root:a,wrapper:i,reactStrictMode:f})},asFragment:()=>{if("function"==typeof document.createRange)return document.createRange().createContextualFragment(n.innerHTML);{const e=document.createElement("template");return e.innerHTML=n.innerHTML,e.content}},...c.getQueriesForElement(r,u)}}function N(e,t){let r,{container:n,baseElement:o=n,legacyRoot:c=!1,onCaughtError:u,onUncaughtError:a,onRecoverableError:i,queries:f,hydrate:s=!1,wrapper:d,reactStrictMode:p}=void 0===t?{}:t;if(void 0!==a)throw new Error("onUncaughtError is not supported. The `render` call will already throw on uncaught errors.");if(c&&"function"!=typeof l.default.render){const e=new Error("`legacyRoot: true` is not supported in this version of React. If your app runs React 19 or later, you should remove this flag. If your app runs React 18 or earlier, visit https://react.dev/blog/2022/03/08/react-18-upgrade-guide for upgrade instructions.");throw Error.captureStackTrace(e,N),e}if(o||(o=document.body),n||(n=o.appendChild(document.createElement("div"))),S.has(n))_.forEach((e=>{e.container===n&&(r=e.root)}));else{r=(c?I:k)(n,{hydrate:s,onCaughtError:u,onRecoverableError:i,ui:e,wrapper:d,reactStrictMode:p}),_.push({container:n,root:r}),S.add(n)}return L(e,{container:n,baseElement:o,queries:f,hydrate:s,wrapper:d,root:r,reactStrictMode:p})}function q(){_.forEach((e=>{let{root:t,container:r}=e;h((()=>{t.unmount()})),r.parentNode===document.body&&document.body.removeChild(r)})),_.length=0,S.clear()}if(("undefined"==typeof process||!process.env?.RTL_SKIP_AUTO_CLEANUP)&&("function"==typeof afterEach?afterEach((()=>{q()})):"function"==typeof teardown&&teardown((()=>{q()})),"function"==typeof beforeAll&&"function"==typeof afterAll)){let e=m();beforeAll((()=>{e=m(),y(!0)})),afterAll((()=>{y(e)}))}e.act=h,e.cleanup=q,e.configure=function(e){"function"==typeof e&&(e=e(j()));const{reactStrictMode:t,...r}=e;c.configure(r),C={...C,reactStrictMode:t}},e.fireEvent=b,e.getConfig=j,e.render=N,e.renderHook=function e(t,r){void 0===r&&(r={});const{initialProps:n,...o}=r;if(o.legacyRoot&&"function"!=typeof l.default.render){const t=new Error("`legacyRoot: true` is not supported in this version of React. If your app runs React 19 or later, you should remove this flag. If your app runs React 18 or earlier, visit https://react.dev/blog/2022/03/08/react-18-upgrade-guide for upgrade instructions.");throw Error.captureStackTrace(t,e),t}const c=i.createRef();function u(e){let{renderCallbackProps:r}=e;const n=t(r);return i.useEffect((()=>{c.current=n})),null}const{rerender:a,unmount:f}=N(i.createElement(u,{renderCallbackProps:n}),o);return{result:c,rerender:function(e){return a(i.createElement(u,{renderCallbackProps:e}))},unmount:f}},Object.keys(c).forEach((function(t){"default"===t||e.hasOwnProperty(t)||Object.defineProperty(e,t,{enumerable:!0,get:function(){return c[t]}})})),Object.defineProperty(e,"__esModule",{value:!0})}));
//# sourceMappingURL=react.umd.min.js.map

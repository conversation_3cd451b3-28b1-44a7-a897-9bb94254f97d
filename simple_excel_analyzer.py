#!/usr/bin/env python3
"""
Simple Excel Data Analyzer
Analyzes Excel files using only built-in Python libraries and openpyxl
"""

import os
import sys
from collections import Counter, defaultdict

try:
    from openpyxl import load_workbook
    OPENPYXL_AVAILABLE = True
except ImportError:
    OPENPYXL_AVAILABLE = False
    print("openpyxl not available. Install it with: pip install openpyxl")

class SimpleExcelAnalyzer:
    def __init__(self):
        self.files = []
        self.data = {}
        
    def find_excel_files(self):
        """Find Excel files in current directory"""
        excel_extensions = ['.xlsx', '.xls']
        current_dir = os.getcwd()
        
        for file in os.listdir(current_dir):
            if any(file.lower().endswith(ext) for ext in excel_extensions):
                self.files.append(file)
        
        print(f"Found Excel files: {self.files}")
        return self.files
    
    def analyze_excel_file(self, filename):
        """Analyze a single Excel file"""
        if not OPENPYXL_AVAILABLE:
            print("Cannot analyze Excel files without openpyxl")
            return None
            
        try:
            print(f"\n📊 Analyzing: {filename}")
            print("="*50)
            
            workbook = load_workbook(filename, data_only=True)
            file_data = {}
            
            print(f"Worksheets: {workbook.sheetnames}")
            
            for sheet_name in workbook.sheetnames:
                sheet = workbook[sheet_name]
                print(f"\n📋 Sheet: {sheet_name}")
                
                # Get dimensions
                max_row = sheet.max_row
                max_col = sheet.max_column
                print(f"Dimensions: {max_row} rows x {max_col} columns")
                
                # Get headers (first row)
                headers = []
                for col in range(1, max_col + 1):
                    cell_value = sheet.cell(row=1, column=col).value
                    headers.append(str(cell_value) if cell_value is not None else f"Column_{col}")
                
                print(f"Headers: {headers}")
                
                # Analyze data in each column
                sheet_data = {
                    'headers': headers,
                    'rows': max_row,
                    'columns': max_col,
                    'column_analysis': {}
                }
                
                for col_idx, header in enumerate(headers, 1):
                    column_data = []
                    for row in range(2, min(max_row + 1, 102)):  # Analyze first 100 data rows
                        cell_value = sheet.cell(row=row, column=col_idx).value
                        if cell_value is not None:
                            column_data.append(str(cell_value))
                    
                    # Analyze column
                    if column_data:
                        unique_values = list(set(column_data))
                        value_counts = Counter(column_data)
                        
                        sheet_data['column_analysis'][header] = {
                            'total_values': len(column_data),
                            'unique_values': len(unique_values),
                            'top_values': value_counts.most_common(5),
                            'sample_values': unique_values[:10]
                        }
                        
                        # Print column analysis
                        print(f"\n  📈 Column: {header}")
                        print(f"    Total values: {len(column_data)}")
                        print(f"    Unique values: {len(unique_values)}")
                        print(f"    Top values: {value_counts.most_common(3)}")
                
                file_data[sheet_name] = sheet_data
            
            self.data[filename] = file_data
            return file_data
            
        except Exception as e:
            print(f"Error analyzing {filename}: {e}")
            return None
    
    def analyze_lead_status(self):
        """Analyze lead status across all files"""
        print("\n" + "="*60)
        print("LEAD STATUS ANALYSIS")
        print("="*60)
        
        status_data = defaultdict(list)
        
        for filename, file_data in self.data.items():
            print(f"\n📊 File: {filename}")
            
            for sheet_name, sheet_data in file_data.items():
                print(f"  📋 Sheet: {sheet_name}")
                
                # Look for status-related columns
                status_columns = []
                for header in sheet_data['headers']:
                    if any(keyword in header.lower() for keyword in 
                          ['status', 'stage', 'state', 'condition', 'progress', 'outcome']):
                        status_columns.append(header)
                
                print(f"    Status columns found: {status_columns}")
                
                for col in status_columns:
                    if col in sheet_data['column_analysis']:
                        analysis = sheet_data['column_analysis'][col]
                        print(f"    📈 {col}:")
                        print(f"      Total entries: {analysis['total_values']}")
                        print(f"      Unique statuses: {analysis['unique_values']}")
                        print(f"      Top statuses: {analysis['top_values']}")
                        
                        # Collect for overall analysis
                        for value, count in analysis['top_values']:
                            status_data[col].extend([value] * count)
        
        # Overall status summary
        if status_data:
            print(f"\n📊 OVERALL STATUS SUMMARY:")
            for column, values in status_data.items():
                total_count = len(values)
                unique_statuses = Counter(values)
                print(f"\n  {column} (Total: {total_count}):")
                for status, count in unique_statuses.most_common():
                    percentage = (count / total_count * 100) if total_count > 0 else 0
                    print(f"    {status}: {count} ({percentage:.1f}%)")
    
    def generate_summary_report(self):
        """Generate a comprehensive summary report"""
        print("\n" + "="*60)
        print("SUMMARY REPORT")
        print("="*60)
        
        total_files = len(self.data)
        total_sheets = sum(len(file_data) for file_data in self.data.values())
        total_rows = 0
        total_columns = 0
        
        for filename, file_data in self.data.items():
            for sheet_name, sheet_data in file_data.items():
                total_rows += sheet_data['rows']
                total_columns += sheet_data['columns']
        
        print(f"📊 OVERVIEW:")
        print(f"  Total Excel files analyzed: {total_files}")
        print(f"  Total worksheets: {total_sheets}")
        print(f"  Total rows across all sheets: {total_rows}")
        print(f"  Total columns across all sheets: {total_columns}")
        
        print(f"\n📋 FILES BREAKDOWN:")
        for filename, file_data in self.data.items():
            print(f"  📄 {filename}:")
            for sheet_name, sheet_data in file_data.items():
                print(f"    📋 {sheet_name}: {sheet_data['rows']} rows x {sheet_data['columns']} columns")
                
                # Identify key columns
                key_columns = []
                for header in sheet_data['headers']:
                    if any(keyword in header.lower() for keyword in 
                          ['id', 'name', 'status', 'date', 'lead', 'service', 'customer']):
                        key_columns.append(header)
                
                if key_columns:
                    print(f"      Key columns: {key_columns}")
    
    def run_analysis(self):
        """Run the complete analysis"""
        print("🚀 Starting Simple Excel Data Analysis")
        print("="*60)
        
        # Find Excel files
        if not self.find_excel_files():
            print("No Excel files found in current directory")
            return
        
        # Analyze each file
        for filename in self.files:
            self.analyze_excel_file(filename)
        
        # Analyze lead status
        self.analyze_lead_status()
        
        # Generate summary
        self.generate_summary_report()
        
        print("\n✅ Analysis completed!")

def main():
    """Main function"""
    print("Current directory:", os.getcwd())
    
    analyzer = SimpleExcelAnalyzer()
    analyzer.run_analysis()

if __name__ == "__main__":
    main()

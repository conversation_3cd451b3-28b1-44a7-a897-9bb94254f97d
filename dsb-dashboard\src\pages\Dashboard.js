import React from 'react';
import { useData } from '../context/DataContext';
import MetricCard from '../components/Dashboard/MetricCard';
import QuickChart from '../components/Dashboard/QuickChart';
import RecentActivity from '../components/Dashboard/RecentActivity';
import TopPerformers from '../components/Dashboard/TopPerformers';
import { 
  FiUsers, 
  FiDollarSign, 
  FiTrendingUp, 
  FiTarget,
  FiActivity,
  FiCalendar,
  FiBarChart3,
  FiPieChart
} from 'react-icons/fi';

const Dashboard = () => {
  const { leadData, statusData, loading, getTotalMetrics, getLeadsByStatus } = useData();

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  const metrics = getTotalMetrics();
  const statusData_chart = getLeadsByStatus();

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="dashboard-header">
        <h1 className="text-3xl font-bold mb-2">DSB Dashboard Overview</h1>
        <p className="text-blue-100">
          Comprehensive analytics for lead management and business performance
        </p>
        <div className="mt-4 flex items-center space-x-4 text-sm">
          <span className="flex items-center">
            <FiCalendar className="mr-1" />
            Last updated: {new Date().toLocaleDateString()}
          </span>
          <span className="flex items-center">
            <FiActivity className="mr-1" />
            Real-time data
          </span>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <MetricCard
          title="Total Leads"
          value={metrics.totalLeads.toLocaleString()}
          icon={FiUsers}
          color="blue"
          trend="+12%"
          trendUp={true}
        />
        <MetricCard
          title="Total Value"
          value={`$${(metrics.totalValue / 1000000).toFixed(1)}M`}
          icon={FiDollarSign}
          color="green"
          trend="+8%"
          trendUp={true}
        />
        <MetricCard
          title="Conversion Rate"
          value={`${metrics.conversionRate}%`}
          icon={FiTarget}
          color="purple"
          trend="+2.3%"
          trendUp={true}
        />
        <MetricCard
          title="Avg Deal Size"
          value={`$${Math.round(metrics.avgValue).toLocaleString()}`}
          icon={FiTrendingUp}
          color="orange"
          trend="-1.2%"
          trendUp={false}
        />
      </div>

      {/* Quick Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="dashboard-card">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-800">Lead Status Distribution</h3>
            <FiPieChart className="text-gray-400" size={20} />
          </div>
          <QuickChart 
            type="pie"
            data={statusData_chart}
            title="Lead Status"
          />
        </div>
        
        <div className="dashboard-card">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-800">Monthly Lead Trends</h3>
            <FiBarChart3 className="text-gray-400" size={20} />
          </div>
          <QuickChart 
            type="line"
            data={leadData.slice(0, 12)}
            title="Monthly Trends"
          />
        </div>
      </div>

      {/* Activity and Performance */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          <RecentActivity leads={leadData.slice(0, 10)} />
        </div>
        <div>
          <TopPerformers data={statusData} />
        </div>
      </div>

      {/* Quick Actions */}
      <div className="dashboard-card">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">Quick Actions</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <button className="flex flex-col items-center p-4 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors">
            <FiUsers className="text-blue-600 mb-2" size={24} />
            <span className="text-sm font-medium text-blue-800">Add New Lead</span>
          </button>
          <button className="flex flex-col items-center p-4 bg-green-50 rounded-lg hover:bg-green-100 transition-colors">
            <FiBarChart3 className="text-green-600 mb-2" size={24} />
            <span className="text-sm font-medium text-green-800">Generate Report</span>
          </button>
          <button className="flex flex-col items-center p-4 bg-purple-50 rounded-lg hover:bg-purple-100 transition-colors">
            <FiTrendingUp className="text-purple-600 mb-2" size={24} />
            <span className="text-sm font-medium text-purple-800">View Analytics</span>
          </button>
          <button className="flex flex-col items-center p-4 bg-orange-50 rounded-lg hover:bg-orange-100 transition-colors">
            <FiDollarSign className="text-orange-600 mb-2" size={24} />
            <span className="text-sm font-medium text-orange-800">Export Data</span>
          </button>
        </div>
      </div>

      {/* Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg p-6 text-white">
          <h4 className="text-lg font-semibold mb-2">This Month</h4>
          <p className="text-3xl font-bold">{leadData.filter(lead => 
            new Date(lead.createdDate).getMonth() === new Date().getMonth()
          ).length}</p>
          <p className="text-blue-100">New Leads</p>
        </div>
        
        <div className="bg-gradient-to-r from-green-500 to-green-600 rounded-lg p-6 text-white">
          <h4 className="text-lg font-semibold mb-2">Closed Won</h4>
          <p className="text-3xl font-bold">{leadData.filter(lead => 
            lead.status === 'Closed Won'
          ).length}</p>
          <p className="text-green-100">Successful Deals</p>
        </div>
        
        <div className="bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg p-6 text-white">
          <h4 className="text-lg font-semibold mb-2">Pipeline Value</h4>
          <p className="text-3xl font-bold">${(leadData
            .filter(lead => !['Closed Won', 'Closed Lost'].includes(lead.status))
            .reduce((sum, lead) => sum + lead.value, 0) / 1000000).toFixed(1)}M</p>
          <p className="text-purple-100">Active Pipeline</p>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;

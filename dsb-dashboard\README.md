# DSB Dashboard - Analytics & Reports

A comprehensive React.js dashboard for DSB lead data analysis with 20 different types of reports including graphical charts, analysis reports, and interactive data grids.

## Features

### 🏠 Dashboard Overview
- Key performance metrics and KPIs
- Quick charts and visualizations
- Recent activity feed
- Top performing services
- Quick action buttons

### 📊 Graphical Reports (8 Types)
1. **Lead Status Distribution** - Pie chart showing lead status breakdown
2. **Service-wise Performance** - Bar chart comparing service performance
3. **Monthly Lead Trends** - Line chart showing lead generation trends
4. **Lead Source Analysis** - Doughnut chart of lead sources
5. **Conversion Funnel** - Funnel visualization of sales stages
6. **Geographic Distribution** - Regional lead distribution
7. **Time-based Activity** - Area chart of activity over time
8. **Performance Metrics** - Gauge charts for KPIs

### 📈 Analysis Reports (7 Types)
1. **Lead Conversion Analysis** - Detailed conversion rate analysis
2. **Service Performance Analytics** - Service comparison and metrics
3. **Trend Analysis Dashboard** - Pattern identification and trends
4. **Comparative Analysis** - Cross-dimensional comparisons
5. **Predictive Analytics** - Future performance forecasting
6. **ROI Analysis** - Return on investment calculations
7. **Customer Segmentation** - Customer value and behavior analysis

### 📋 Grid Reports (5 Types)
1. **Lead Data Grid** - Complete lead information with sorting/filtering
2. **Service Status Grid** - Service performance data table
3. **Performance Summary Grid** - Key metrics summary table
4. **Detailed Analytics Grid** - Advanced analytics data
5. **Export/Import Data Grid** - Data management tools

## Technology Stack

- **Frontend**: React 18, React Router DOM
- **Styling**: Tailwind CSS
- **Charts**: Recharts, Chart.js, React-ChartJS-2
- **Data Grid**: Custom implementation with sorting/filtering
- **Icons**: React Icons (Feather Icons)
- **Data Processing**: XLSX for Excel file handling
- **State Management**: React Context API

## Available Scripts

In the project directory, you can run:

### `npm start`

Runs the app in the development mode.\
Open [http://localhost:3000](http://localhost:3000) to view it in your browser.

The page will reload when you make changes.\
You may also see any lint errors in the console.

### `npm test`

Launches the test runner in the interactive watch mode.\
See the section about [running tests](https://facebook.github.io/create-react-app/docs/running-tests) for more information.

### `npm run build`

Builds the app for production to the `build` folder.\
It correctly bundles React in production mode and optimizes the build for the best performance.

The build is minified and the filenames include the hashes.\
Your app is ready to be deployed!

See the section about [deployment](https://facebook.github.io/create-react-app/docs/deployment) for more information.

### `npm run eject`

**Note: this is a one-way operation. Once you `eject`, you can't go back!**

If you aren't satisfied with the build tool and configuration choices, you can `eject` at any time. This command will remove the single build dependency from your project.

Instead, it will copy all the configuration files and the transitive dependencies (webpack, Babel, ESLint, etc) right into your project so you have full control over them. All of the commands except `eject` will still work, but they will point to the copied scripts so you can tweak them. At this point you're on your own.

You don't have to ever use `eject`. The curated feature set is suitable for small and middle deployments, and you shouldn't feel obligated to use this feature. However we understand that this tool wouldn't be useful if you couldn't customize it when you are ready for it.

## Learn More

You can learn more in the [Create React App documentation](https://facebook.github.io/create-react-app/docs/getting-started).

To learn React, check out the [React documentation](https://reactjs.org/).

### Code Splitting

This section has moved here: [https://facebook.github.io/create-react-app/docs/code-splitting](https://facebook.github.io/create-react-app/docs/code-splitting)

### Analyzing the Bundle Size

This section has moved here: [https://facebook.github.io/create-react-app/docs/analyzing-the-bundle-size](https://facebook.github.io/create-react-app/docs/analyzing-the-bundle-size)

### Making a Progressive Web App

This section has moved here: [https://facebook.github.io/create-react-app/docs/making-a-progressive-web-app](https://facebook.github.io/create-react-app/docs/making-a-progressive-web-app)

### Advanced Configuration

This section has moved here: [https://facebook.github.io/create-react-app/docs/advanced-configuration](https://facebook.github.io/create-react-app/docs/advanced-configuration)

### Deployment

This section has moved here: [https://facebook.github.io/create-react-app/docs/deployment](https://facebook.github.io/create-react-app/docs/deployment)

### `npm run build` fails to minify

This section has moved here: [https://facebook.github.io/create-react-app/docs/troubleshooting#npm-run-build-fails-to-minify](https://facebook.github.io/create-react-app/docs/troubleshooting#npm-run-build-fails-to-minify)
